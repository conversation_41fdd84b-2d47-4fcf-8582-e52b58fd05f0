extends Node
class_name GameStats

# 游戏统计系统
# 记录和管理游戏统计数据

# 统计数据结构
var total_games_played: int = 0
var games_won: int = 0
var games_lost: int = 0
var total_cards_played: int = 0
var fastest_win_time: float = 0.0
var longest_game_time: float = 0.0
var favorite_card_rank: Card.Rank = Card.Rank.THREE
var win_rate: float = 0.0

# 当前游戏统计
var current_game_start_time: float = 0.0
var current_game_cards_played: int = 0
var current_game_turns: int = 0

signal stats_updated()

func _ready():
	load_stats()

# 开始新游戏统计
func start_new_game():
	current_game_start_time = Time.get_time_dict_from_system()["unix"]
	current_game_cards_played = 0
	current_game_turns = 0
	print("开始记录新游戏统计")

# 记录出牌
func record_card_played(cards: Array[Card]):
	current_game_cards_played += cards.size()
	total_cards_played += cards.size()
	
	# 统计最常用的牌
	for card in cards:
		update_favorite_card(card.rank)

# 记录回合
func record_turn():
	current_game_turns += 1

# 游戏结束统计
func end_game(player_won: bool):
	total_games_played += 1
	
	if player_won:
		games_won += 1
	else:
		games_lost += 1
	
	# 计算游戏时长
	var current_time = Time.get_time_dict_from_system()["unix"]
	var game_duration = current_time - current_game_start_time
	
	# 更新最快/最长游戏时间
	if player_won and (fastest_win_time == 0.0 or game_duration < fastest_win_time):
		fastest_win_time = game_duration
	
	if game_duration > longest_game_time:
		longest_game_time = game_duration
	
	# 更新胜率
	win_rate = float(games_won) / float(total_games_played) * 100.0
	
	print("游戏结束统计:")
	print("- 游戏时长: ", format_time(game_duration))
	print("- 出牌数: ", current_game_cards_played)
	print("- 回合数: ", current_game_turns)
	print("- 总胜率: ", "%.1f%%" % win_rate)
	
	save_stats()
	stats_updated.emit()

# 更新最喜欢的牌
func update_favorite_card(rank: Card.Rank):
	# 这里可以实现更复杂的统计逻辑
	# 暂时简单记录
	favorite_card_rank = rank

# 获取统计摘要
func get_stats_summary() -> String:
	var summary = "游戏统计:\n"
	summary += "总游戏数: " + str(total_games_played) + "\n"
	summary += "获胜: " + str(games_won) + " 失败: " + str(games_lost) + "\n"
	summary += "胜率: " + "%.1f%%" % win_rate + "\n"
	summary += "总出牌数: " + str(total_cards_played) + "\n"
	
	if fastest_win_time > 0:
		summary += "最快获胜: " + format_time(fastest_win_time) + "\n"
	
	if longest_game_time > 0:
		summary += "最长游戏: " + format_time(longest_game_time) + "\n"
	
	return summary

# 格式化时间显示
func format_time(seconds: float) -> String:
	var minutes = int(seconds) / 60
	var secs = int(seconds) % 60
	return "%d:%02d" % [minutes, secs]

# 重置统计
func reset_stats():
	total_games_played = 0
	games_won = 0
	games_lost = 0
	total_cards_played = 0
	fastest_win_time = 0.0
	longest_game_time = 0.0
	win_rate = 0.0
	
	save_stats()
	stats_updated.emit()
	print("统计数据已重置")

# 保存统计数据
func save_stats():
	var stats_data = {
		"total_games_played": total_games_played,
		"games_won": games_won,
		"games_lost": games_lost,
		"total_cards_played": total_cards_played,
		"fastest_win_time": fastest_win_time,
		"longest_game_time": longest_game_time,
		"favorite_card_rank": favorite_card_rank,
		"win_rate": win_rate
	}
	
	var file = FileAccess.open("user://game_stats.dat", FileAccess.WRITE)
	if file:
		var json_string = JSON.stringify(stats_data)
		file.store_string(json_string)
		file.close()

# 加载统计数据
func load_stats():
	if not FileAccess.file_exists("user://game_stats.dat"):
		return
	
	var file = FileAccess.open("user://game_stats.dat", FileAccess.READ)
	if not file:
		return
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	if parse_result != OK:
		return
	
	var stats_data = json.data
	total_games_played = stats_data.get("total_games_played", 0)
	games_won = stats_data.get("games_won", 0)
	games_lost = stats_data.get("games_lost", 0)
	total_cards_played = stats_data.get("total_cards_played", 0)
	fastest_win_time = stats_data.get("fastest_win_time", 0.0)
	longest_game_time = stats_data.get("longest_game_time", 0.0)
	favorite_card_rank = stats_data.get("favorite_card_rank", Card.Rank.THREE)
	win_rate = stats_data.get("win_rate", 0.0)
	
	print("统计数据已加载")

# 获取详细统计
func get_detailed_stats() -> Dictionary:
	return {
		"total_games": total_games_played,
		"wins": games_won,
		"losses": games_lost,
		"win_rate": win_rate,
		"total_cards": total_cards_played,
		"fastest_win": fastest_win_time,
		"longest_game": longest_game_time,
		"avg_cards_per_game": float(total_cards_played) / max(1, total_games_played)
	}

# 比较统计
func compare_with_average() -> String:
	var avg_cards_per_game = float(total_cards_played) / max(1, total_games_played)
	var comparison = "与平均水平比较:\n"
	
	if win_rate > 50.0:
		comparison += "胜率高于平均水平 ✓\n"
	else:
		comparison += "胜率需要提高 ✗\n"
	
	if avg_cards_per_game < 15.0:
		comparison += "出牌效率较高 ✓\n"
	else:
		comparison += "可以提高出牌效率 ✗\n"
	
	return comparison
