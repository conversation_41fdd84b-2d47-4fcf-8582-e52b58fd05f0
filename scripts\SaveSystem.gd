extends Node
class_name SaveSystem

# 游戏存档系统
# 负责保存和加载游戏状态

const SAVE_FILE_PATH = "user://game_save.dat"
const CONFIG_FILE_PATH = "user://game_config.dat"

# 游戏存档数据结构
class GameSaveData:
	var player_hands: Array = []
	var current_player_index: int = 0
	var landlord_index: int = -1
	var bottom_cards: Array = []
	var last_played_cards: Array = []
	var game_state: int = 0
	var timestamp: String = ""
	
	func _init():
		timestamp = Time.get_datetime_string_from_system()

# 保存游戏状态
static func save_game(game_manager) -> bool:
	if not game_manager:
		print("错误：游戏管理器为空，无法保存")
		return false
	
	var save_data = GameSaveData.new()
	
	# 保存玩家手牌
	var players = game_manager.get_players()
	for player in players:
		var hand_data = []
		for card in player.get_hand_cards():
			hand_data.append({
				"suit": card.suit,
				"rank": card.rank
			})
		save_data.player_hands.append(hand_data)
	
	# 保存游戏状态
	save_data.current_player_index = game_manager.current_player_index
	save_data.landlord_index = game_manager.landlord_index
	save_data.game_state = game_manager.current_state
	
	# 保存底牌
	for card in game_manager.bottom_cards:
		save_data.bottom_cards.append({
			"suit": card.suit,
			"rank": card.rank
		})
	
	# 保存上次出牌
	for card in game_manager.last_played_cards:
		save_data.last_played_cards.append({
			"suit": card.suit,
			"rank": card.rank
		})
	
	# 写入文件
	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.WRITE)
	if file:
		var json_string = JSON.stringify(var_to_dict(save_data))
		file.store_string(json_string)
		file.close()
		print("游戏已保存到: ", SAVE_FILE_PATH)
		return true
	else:
		print("错误：无法创建存档文件")
		return false

# 加载游戏状态
static func load_game(game_manager) -> bool:
	if not FileAccess.file_exists(SAVE_FILE_PATH):
		print("存档文件不存在")
		return false
	
	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
	if not file:
		print("错误：无法打开存档文件")
		return false
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	if parse_result != OK:
		print("错误：存档文件格式错误")
		return false
	
	var save_data = json.data
	
	# 恢复游戏状态
	try_restore_game_state(game_manager, save_data)
	
	print("游戏已从存档加载")
	return true

static func try_restore_game_state(game_manager, save_data: Dictionary):
	# 这里应该恢复游戏状态
	# 由于当前的游戏管理器结构，这需要一些重构
	# 暂时只打印加载信息
	print("尝试恢复游戏状态...")
	print("存档时间: ", save_data.get("timestamp", "未知"))
	print("当前玩家索引: ", save_data.get("current_player_index", 0))
	print("地主索引: ", save_data.get("landlord_index", -1))

# 检查是否有存档
static func has_save_file() -> bool:
	return FileAccess.file_exists(SAVE_FILE_PATH)

# 删除存档
static func delete_save_file() -> bool:
	if FileAccess.file_exists(SAVE_FILE_PATH):
		var dir = DirAccess.open("user://")
		if dir:
			dir.remove(SAVE_FILE_PATH.get_file())
			print("存档文件已删除")
			return true
	return false

# 获取存档信息
static func get_save_info() -> Dictionary:
	if not has_save_file():
		return {}
	
	var file = FileAccess.open(SAVE_FILE_PATH, FileAccess.READ)
	if not file:
		return {}
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	if parse_result != OK:
		return {}
	
	var save_data = json.data
	return {
		"timestamp": save_data.get("timestamp", "未知"),
		"current_player": save_data.get("current_player_index", 0),
		"game_state": save_data.get("game_state", 0)
	}

# 保存游戏配置
static func save_config(config: GameConfig) -> bool:
	var file = FileAccess.open(CONFIG_FILE_PATH, FileAccess.WRITE)
	if file:
		var config_dict = {
			"master_volume": config.master_volume if config.has_method("get") else 1.0,
			"game_speed": config.game_speed if config else 1.0,
			"auto_sort_cards": config.auto_sort_cards if config else true,
			"ai_difficulty": config.ai_difficulty if config else 1
		}
		var json_string = JSON.stringify(config_dict)
		file.store_string(json_string)
		file.close()
		print("配置已保存")
		return true
	return false

# 加载游戏配置
static func load_config() -> Dictionary:
	if not FileAccess.file_exists(CONFIG_FILE_PATH):
		return {}
	
	var file = FileAccess.open(CONFIG_FILE_PATH, FileAccess.READ)
	if not file:
		return {}
	
	var json_string = file.get_as_text()
	file.close()
	
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	if parse_result != OK:
		return {}
	
	return json.data

# 创建卡牌数据
static func create_card_data(card: Card) -> Dictionary:
	return {
		"suit": card.suit,
		"rank": card.rank
	}

# 从数据创建卡牌
static func create_card_from_data(card_data: Dictionary) -> Card:
	var card = Card.new()
	card.suit = card_data.get("suit", Card.Suit.SPADES)
	card.rank = card_data.get("rank", Card.Rank.THREE)
	return card
