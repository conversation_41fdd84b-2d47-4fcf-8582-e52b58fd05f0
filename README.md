# 斗地主游戏

一个使用Godot 4.4.1引擎和GDScript语言开发的简单斗地主游戏。

## 功能特性

- ✅ 完整的54张牌牌堆（包括大小王）
- ✅ 3个玩家（1个人类玩家 + 2个AI）
- ✅ 鼠标左键选牌交互
- ✅ 基础的出牌和过牌功能
- ✅ 简单的AI对手
- ✅ 游戏状态显示
- ✅ 自动发牌和洗牌

## 游戏控制

### 鼠标操作
- **左键点击卡牌**: 选择/取消选择手牌
- **选中的卡牌**: 会高亮显示并向上移动

### 按钮操作
- **出牌**: 打出当前选中的卡牌
- **过牌**: 跳过当前回合
- **新游戏**: 重新开始一局新游戏

## 游戏规则

### 基础规则
1. 每局游戏3个玩家，每人发17张牌，剩余3张作为底牌
2. 地主获得底牌（当前版本中玩家自动成为地主）
3. 地主先出牌，其他玩家按顺序出牌
4. 必须压过上家的牌，或者选择过牌
5. 最先出完手牌的玩家获胜

### 当前实现的牌型
- **单张**: 任意一张牌
- **基础比较**: 按牌面大小比较（3 < 4 < ... < K < A < 2 < 小王 < 大王）

## 项目结构

```
├── scenes/
│   ├── Main.tscn          # 主游戏场景
│   └── Card.tscn          # 卡牌场景
├── scripts/
│   ├── MainController.gd  # 主控制器
│   ├── GameManager.gd     # 游戏管理器
│   ├── Card.gd           # 卡牌类
│   ├── Deck.gd           # 牌堆管理
│   ├── Player.gd         # 玩家类
│   └── CardArea.gd       # 卡牌区域管理
├── textures/             # 贴图资源文件夹
├── sounds/               # 音效资源文件夹
└── project.godot         # Godot项目配置
```

## 如何运行

1. 确保已安装Godot 4.4.1或更高版本
2. 用Godot编辑器打开项目文件夹
3. 点击"运行"按钮或按F5键启动游戏

## 开发说明

### 核心类说明

- **Card**: 扑克牌类，包含花色、点数、选择状态等
- **Deck**: 牌堆管理，负责创建、洗牌、发牌
- **Player**: 玩家类，管理手牌和AI逻辑
- **GameManager**: 游戏主控制器，管理游戏流程和状态
- **CardArea**: 卡牌区域管理，负责卡牌的排列和显示
- **MainController**: UI控制器，连接游戏逻辑和用户界面

### 扩展建议

1. **增加更多牌型**: 对子、三张、顺子、炸弹等
2. **改进AI**: 更智能的出牌策略
3. **添加音效**: 出牌、选牌、游戏结束等音效
4. **美化界面**: 更好的卡牌贴图和UI设计
5. **叫地主系统**: 实现完整的叫地主流程
6. **计分系统**: 添加游戏积分和统计

## 素材资源

当前版本使用简单的文字显示卡牌，可以从以下开源资源获取更好的素材：

- **卡牌贴图**: [OpenGameArt.org](https://opengameart.org/content/playing-cards-vector-png)
- **音效**: [Freesound.org](https://freesound.org/)
- **UI素材**: [Kenney.nl](https://kenney.nl/assets)

## 最新功能

### 已实现功能 ✅
- **完整的游戏逻辑**: 发牌、出牌、过牌、游戏结束判定
- **音效系统**: 选牌、出牌、按钮点击等音效支持
- **游戏统计**: 胜率、游戏时长、出牌统计等
- **存档系统**: 游戏状态保存和加载
- **配置系统**: 游戏参数自定义
- **AI对手**: 基础的AI出牌逻辑

### 技术特性
- **模块化设计**: 各个系统独立，易于扩展
- **事件驱动**: 使用信号系统进行组件通信
- **数据持久化**: 统计和配置数据自动保存
- **用户友好**: 直观的鼠标操作和UI反馈

## 快速开始

1. **安装Godot**: 下载并安装Godot 4.4.1或更高版本
2. **打开项目**: 用Godot编辑器打开`project.godot`文件
3. **运行游戏**: 点击运行按钮或按F5键
4. **开始游戏**: 点击"新游戏"按钮开始一局斗地主

## 许可证

本项目采用MIT许可证，可自由使用和修改。
