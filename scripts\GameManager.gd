extends Node
class_name GameManager

# 游戏管理器
enum GameState { WAITING, BIDDING, PLAYING, GAME_OVER }

@export var current_state: GameState = GameState.WAITING

var deck: Deck
var players: Array[Player] = []
var current_player_index: int = 0
var landlord_index: int = -1
var bottom_cards = []
var last_played_cards = []
var last_player_index: int = -1
var pass_count: int = 0
var game_stats

signal game_state_changed(new_state: GameState)
signal turn_changed(player_index: int)
signal cards_played(player_index: int, cards)
signal game_over(winner_index: int)

func _ready():
	initialize_game()

func initialize_game():
	# 初始化游戏
	deck = Deck.new()
	add_child(deck)

	# 初始化统计系统
	var stats_scene = preload("res://scripts/GameStats.gd")
	game_stats = stats_scene.new()
	add_child(game_stats)

	# 创建3个玩家
	create_players()

	# 开始新游戏
	start_new_game()

func create_players():
	players.clear()
	
	# 玩家1 (人类)
	var human_player = Player.new()
	human_player.player_type = Player.PlayerType.HUMAN
	human_player.player_name = "玩家"
	human_player.cards_played.connect(_on_player_cards_played)
	human_player.turn_passed.connect(_on_player_turn_passed)
	add_child(human_player)
	players.append(human_player)
	
	# 玩家2 (AI)
	var ai_player1 = Player.new()
	ai_player1.player_type = Player.PlayerType.AI
	ai_player1.player_name = "电脑1"
	ai_player1.cards_played.connect(_on_player_cards_played)
	ai_player1.turn_passed.connect(_on_player_turn_passed)
	add_child(ai_player1)
	players.append(ai_player1)
	
	# 玩家3 (AI)
	var ai_player2 = Player.new()
	ai_player2.player_type = Player.PlayerType.AI
	ai_player2.player_name = "电脑2"
	ai_player2.cards_played.connect(_on_player_cards_played)
	ai_player2.turn_passed.connect(_on_player_turn_passed)
	add_child(ai_player2)
	players.append(ai_player2)

func start_new_game():
	print("开始新游戏")

	# 开始统计
	if game_stats:
		game_stats.start_new_game()

	# 洗牌并发牌
	deck.shuffle()
	var deal_result = deck.deal_cards(3)
	var player_hands = deal_result[0]
	bottom_cards = deal_result[1]

	# 给每个玩家发牌
	for i in range(players.size()):
		players[i].add_cards(player_hands[i])

	# 简单的地主选择（暂时让玩家1当地主）
	set_landlord(0)

	# 开始游戏
	change_state(GameState.PLAYING)
	start_turn(landlord_index)

func set_landlord(player_index: int):
	landlord_index = player_index
	players[player_index].is_landlord = true
	
	# 地主获得底牌
	for card in bottom_cards:
		players[player_index].add_card(card)
	
	print(players[player_index].player_name, " 成为地主")

func change_state(new_state: GameState):
	current_state = new_state
	game_state_changed.emit(new_state)

func start_turn(player_index: int):
	current_player_index = player_index
	turn_changed.emit(player_index)
	
	print("轮到 ", players[player_index].player_name, " 出牌")
	
	# 如果是AI玩家，自动出牌
	if players[player_index].is_ai():
		call_deferred("ai_play_turn", player_index)

func ai_play_turn(player_index: int):
	var player = players[player_index]
	var cards_to_play = player.ai_make_decision(last_played_cards)
	
	if cards_to_play.size() > 0:
		play_cards(player_index, cards_to_play)
	else:
		pass_turn(player_index)

func play_cards(player_index: int, cards):
	var player = players[player_index]

	if player.can_play_cards(cards):
		player.remove_cards(cards)
		last_played_cards = cards
		last_player_index = player_index
		pass_count = 0

		# 记录统计
		if game_stats:
			game_stats.record_card_played(cards)

		cards_played.emit(player_index, cards)
		print(player.player_name, " 出牌: ", get_cards_string(cards))

		# 检查游戏是否结束
		if player.get_hand_count() == 0:
			end_game(player_index)
			return

		# 下一个玩家
		next_turn()

func pass_turn(player_index: int):
	pass_count += 1
	print(players[player_index].player_name, " 过牌")
	
	# 如果连续两个玩家过牌，清空上次出牌
	if pass_count >= 2:
		last_played_cards.clear()
		last_player_index = -1
		pass_count = 0
		print("清空上次出牌")
	
	next_turn()

func next_turn():
	current_player_index = (current_player_index + 1) % players.size()

	# 记录回合统计
	if game_stats:
		game_stats.record_turn()

	start_turn(current_player_index)

func end_game(winner_index: int):
	change_state(GameState.GAME_OVER)

	# 记录游戏结束统计
	if game_stats:
		var player_won = (winner_index == 0)  # 假设玩家是索引0
		game_stats.end_game(player_won)

	game_over.emit(winner_index)
	print("游戏结束！", players[winner_index].player_name, " 获胜！")

func get_cards_string(cards) -> String:
	var result = ""
	for card in cards:
		result += card.get_card_name() + " "
	return result.strip_edges()

func _on_player_cards_played(_cards):
	# 这个信号由Player发出，但我们需要知道是哪个玩家
	# 在实际实现中，可能需要传递玩家索引
	pass

func _on_player_turn_passed():
	# 玩家选择过牌
	pass_turn(current_player_index)

func get_current_player() -> Player:
	if current_player_index >= 0 and current_player_index < players.size():
		return players[current_player_index]
	return null

func get_player(index: int) -> Player:
	if index >= 0 and index < players.size():
		return players[index]
	return null

func get_players() -> Array[Player]:
	return players
