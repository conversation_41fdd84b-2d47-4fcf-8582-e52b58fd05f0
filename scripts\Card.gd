extends Control
class_name Card

# 扑克牌类
enum Suit { SPADES, HEARTS, DIAMONDS, CLUBS }
enum Rank { THREE = 3, FOUR, FIVE, SIX, SEVEN, EIGHT, NINE, TEN, JACK, QUEEN, KING, ACE, TWO, <PERSON><PERSON><PERSON>_JOKER, B<PERSON>_JOKER }

@export var suit: Suit
@export var rank: Rank
@export var is_selected: bool = false
@export var is_face_up: bool = true

@onready var card_sprite: Sprite2D = $CardSprite
@onready var card_back: Sprite2D = $CardBack
@onready var selection_highlight: ColorRect = $SelectionHighlight
@onready var card_text: Label = $CardText

var original_position: Vector2
var original_scale: Vector2 = Vector2.ONE
var hover_scale: Vector2 = Vector2(1.1, 1.1)
var selected_offset: Vector2 = Vector2(0, -20)

signal card_clicked(card: Card)
signal card_hovered(card: Card)

func _ready():
	original_position = position
	original_scale = scale
	
	# 设置鼠标事件
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)
	gui_input.connect(_on_gui_input)
	
	update_card_display()

func _on_mouse_entered():
	if not is_selected:
		var tween = create_tween()
		tween.tween_property(self, "scale", hover_scale, 0.1)
	card_hovered.emit(self)

func _on_mouse_exited():
	if not is_selected:
		var tween = create_tween()
		tween.tween_property(self, "scale", original_scale, 0.1)

func _on_gui_input(event: InputEvent):
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
			toggle_selection()
			card_clicked.emit(self)

func toggle_selection():
	is_selected = !is_selected
	update_selection_display()

func set_selected(selected: bool):
	is_selected = selected
	update_selection_display()

func update_selection_display():
	selection_highlight.visible = is_selected
	
	var tween = create_tween()
	if is_selected:
		tween.parallel().tween_property(self, "position", original_position + selected_offset, 0.2)
		tween.parallel().tween_property(self, "scale", hover_scale, 0.2)
	else:
		tween.parallel().tween_property(self, "position", original_position, 0.2)
		tween.parallel().tween_property(self, "scale", original_scale, 0.2)

func update_card_display():
	if is_face_up:
		card_sprite.visible = true
		card_back.visible = false
		card_text.visible = true
		card_text.text = get_card_name()

		# 设置文字颜色
		if suit == Suit.HEARTS or suit == Suit.DIAMONDS:
			card_text.modulate = Color.RED
		else:
			card_text.modulate = Color.BLACK
	else:
		card_sprite.visible = false
		card_back.visible = true
		card_text.visible = false

func get_card_value() -> int:
	# 返回牌的大小值，用于比较
	if rank == Rank.BIG_JOKER:
		return 17
	elif rank == Rank.SMALL_JOKER:
		return 16
	elif rank == Rank.TWO:
		return 15
	elif rank == Rank.ACE:
		return 14
	else:
		return rank

func get_card_name() -> String:
	var suit_names = ["♠", "♥", "♦", "♣"]
	var rank_names = ["", "", "", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A", "2", "小王", "大王"]
	
	if rank == Rank.SMALL_JOKER:
		return "小王"
	elif rank == Rank.BIG_JOKER:
		return "大王"
	else:
		return suit_names[suit] + rank_names[rank]

func is_joker() -> bool:
	return rank == Rank.SMALL_JOKER or rank == Rank.BIG_JOKER

func set_position_smoothly(new_pos: Vector2, duration: float = 0.3):
	var tween = create_tween()
	tween.tween_property(self, "position", new_pos, duration)
	original_position = new_pos
