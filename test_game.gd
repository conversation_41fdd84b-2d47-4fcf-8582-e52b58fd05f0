extends Node

# 简单的游戏逻辑测试脚本

func _ready():
	print("开始测试斗地主游戏逻辑...")
	test_deck()
	test_card()
	test_player()
	print("测试完成！")

func test_deck():
	print("\n=== 测试牌堆 ===")
	var DeckScript = load("res://scripts/Deck.gd")
	var deck = DeckScript.new()
	add_child(deck)
	
	print("牌堆大小: ", deck.get_card_count())
	assert(deck.get_card_count() == 54, "牌堆应该有54张牌")
	
	deck.shuffle()
	print("洗牌完成")
	
	var deal_result = deck.deal_cards(3)
	var player_hands = deal_result[0]
	var bottom_cards = deal_result[1]
	
	print("玩家1手牌数: ", player_hands[0].size())
	print("玩家2手牌数: ", player_hands[1].size())
	print("玩家3手牌数: ", player_hands[2].size())
	print("底牌数: ", bottom_cards.size())
	
	assert(player_hands[0].size() == 17, "每个玩家应该有17张牌")
	assert(player_hands[1].size() == 17, "每个玩家应该有17张牌")
	assert(player_hands[2].size() == 17, "每个玩家应该有17张牌")
	assert(bottom_cards.size() == 3, "应该有3张底牌")
	
	deck.queue_free()

func test_card():
	print("\n=== 测试卡牌 ===")
	var CardScript = load("res://scripts/Card.gd")
	var card = CardScript.new()
	card.suit = 0  # SPADES
	card.rank = 14  # ACE
	
	print("卡牌名称: ", card.get_card_name())
	print("卡牌值: ", card.get_card_value())
	
	assert(card.get_card_name() == "♠A", "黑桃A的名称应该是♠A")
	assert(card.get_card_value() == 14, "A的值应该是14")
	
	var joker = CardScript.new()
	joker.rank = 17  # BIG_JOKER
	print("大王名称: ", joker.get_card_name())
	print("大王值: ", joker.get_card_value())
	
	assert(joker.is_joker(), "大王应该被识别为王牌")
	assert(joker.get_card_value() == 17, "大王的值应该是17")

func test_player():
	print("\n=== 测试玩家 ===")
	var PlayerScript = load("res://scripts/Player.gd")
	var player = PlayerScript.new()
	player.player_name = "测试玩家"

	# 创建一些测试卡牌
	var cards = []
	var CardScript = load("res://scripts/Card.gd")
	for i in range(5):
		var card = CardScript.new()
		card.suit = 0  # SPADES
		card.rank = 3 + i  # THREE + i
		cards.append(card)
	
	player.add_cards(cards)
	print("玩家手牌数: ", player.get_hand_count())
	assert(player.get_hand_count() == 5, "玩家应该有5张牌")
	
	# 测试选牌
	player.select_card(cards[0])
	player.select_card(cards[1])
	print("选中卡牌数: ", player.get_selected_cards().size())
	assert(player.get_selected_cards().size() == 2, "应该选中2张牌")
	
	# 测试出牌
	var played = player.play_selected_cards()
	print("出牌数: ", played.size())
	print("剩余手牌数: ", player.get_hand_count())
	assert(played.size() == 2, "应该出了2张牌")
	assert(player.get_hand_count() == 3, "应该剩余3张牌")

# 运行测试的辅助函数
func run_tests():
	_ready()
