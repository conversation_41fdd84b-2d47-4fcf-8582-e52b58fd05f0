extends Node
class_name AudioManager

# 音效管理器
# 负责管理游戏中的所有音效播放

# 音效类型枚举
enum SoundType {
	CARD_SELECT,    # 选牌音效
	CARD_PLAY,      # 出牌音效
	CARD_SHUFFLE,   # 洗牌音效
	GAME_START,     # 游戏开始音效
	GAME_WIN,       # 获胜音效
	GAME_LOSE,      # 失败音效
	BUTTON_CLICK,   # 按钮点击音效
	PASS_TURN       # 过牌音效
}

# 音效播放器池
var audio_players: Array[AudioStreamPlayer] = []
var max_players: int = 10  # 最大同时播放的音效数量

# 音效资源字典
var sound_resources: Dictionary = {}

# 音量设置
@export var master_volume: float = 1.0
@export var sfx_volume: float = 0.8
@export var is_muted: bool = false

signal sound_played(sound_type: SoundType)

func _ready():
	# 初始化音效播放器池
	create_audio_player_pool()
	
	# 加载音效资源
	load_sound_resources()

func create_audio_player_pool():
	for i in range(max_players):
		var player = AudioStreamPlayer.new()
		add_child(player)
		audio_players.append(player)

func load_sound_resources():
	# 这里加载音效资源文件
	# 由于我们还没有实际的音效文件，先创建占位符
	
	# 示例：如果有音效文件，可以这样加载
	# sound_resources[SoundType.CARD_SELECT] = preload("res://sounds/card_select.ogg")
	# sound_resources[SoundType.CARD_PLAY] = preload("res://sounds/card_play.ogg")
	
	print("音效资源加载完成（当前使用占位符）")

func play_sound(sound_type: SoundType, volume_scale: float = 1.0):
	if is_muted:
		return
	
	# 查找可用的音效播放器
	var available_player = get_available_player()
	if not available_player:
		print("警告：没有可用的音效播放器")
		return
	
	# 获取音效资源
	var sound_resource = sound_resources.get(sound_type)
	if not sound_resource:
		# 如果没有实际音效文件，播放默认音效或跳过
		play_default_sound(sound_type, available_player, volume_scale)
		return
	
	# 设置音效并播放
	available_player.stream = sound_resource
	available_player.volume_db = linear_to_db(master_volume * sfx_volume * volume_scale)
	available_player.play()
	
	sound_played.emit(sound_type)
	print("播放音效: ", SoundType.keys()[sound_type])

func play_default_sound(sound_type: SoundType, player: AudioStreamPlayer, volume_scale: float):
	# 播放默认音效或简单的提示音
	# 这里可以生成简单的程序化音效
	
	match sound_type:
		SoundType.CARD_SELECT:
			# 播放一个简短的提示音
			create_simple_beep(player, 800, 0.1, volume_scale)
		SoundType.CARD_PLAY:
			create_simple_beep(player, 600, 0.2, volume_scale)
		SoundType.BUTTON_CLICK:
			create_simple_beep(player, 1000, 0.05, volume_scale)
		_:
			# 默认音效
			create_simple_beep(player, 500, 0.1, volume_scale)

func create_simple_beep(player: AudioStreamPlayer, frequency: float, duration: float, volume_scale: float):
	# 创建简单的程序化音效
	var generator = AudioStreamGenerator.new()
	generator.mix_rate = 22050
	generator.buffer_length = duration
	
	player.stream = generator
	player.volume_db = linear_to_db(master_volume * sfx_volume * volume_scale)
	player.play()
	
	# 这里应该生成实际的音频数据，但为了简化，我们只是播放静音
	# 在实际项目中，你可能想要使用更复杂的音频生成

func get_available_player() -> AudioStreamPlayer:
	# 查找不在播放状态的音效播放器
	for player in audio_players:
		if not player.playing:
			return player
	
	# 如果所有播放器都在使用，返回第一个（会中断当前播放）
	return audio_players[0] if audio_players.size() > 0 else null

func stop_all_sounds():
	for player in audio_players:
		if player.playing:
			player.stop()

func set_master_volume(volume: float):
	master_volume = clamp(volume, 0.0, 1.0)

func set_sfx_volume(volume: float):
	sfx_volume = clamp(volume, 0.0, 1.0)

func set_muted(muted: bool):
	is_muted = muted
	if muted:
		stop_all_sounds()

func get_volume_db(linear_volume: float) -> float:
	return linear_to_db(linear_volume) if linear_volume > 0 else -80.0

# 便捷方法
func play_card_select():
	play_sound(SoundType.CARD_SELECT)

func play_card_play():
	play_sound(SoundType.CARD_PLAY)

func play_button_click():
	play_sound(SoundType.BUTTON_CLICK)

func play_game_start():
	play_sound(SoundType.GAME_START)

func play_game_win():
	play_sound(SoundType.GAME_WIN)

func play_game_lose():
	play_sound(SoundType.GAME_LOSE)
