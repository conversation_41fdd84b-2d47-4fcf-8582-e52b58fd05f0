extends Node
class_name Deck

# 牌堆管理类
var cards: Array[Card] = []

func _ready():
	create_deck()

func create_deck():
	cards.clear()
	
	# 创建普通牌 (3-A, 每种花色各一张)
	for suit in Card.Suit.values():
		for rank in range(Card.Rank.THREE, Card.Rank.TWO + 1):
			if rank <= Card.Rank.ACE:  # 3-A
				var card = create_card(suit, rank)
				cards.append(card)
	
	# 添加2 (每种花色各一张)
	for suit in Card.Suit.values():
		var card = create_card(suit, Card.Rank.TWO)
		cards.append(card)
	
	# 添加大小王
	var small_joker = create_card(Card.Suit.SPADES, Card.Rank.SMALL_JOKER)  # 花色对王牌无意义
	var big_joker = create_card(Card.Suit.SPADES, Card.Rank.BIG_JOKER)
	cards.append(small_joker)
	cards.append(big_joker)
	
	print("牌堆创建完成，共 ", cards.size(), " 张牌")

func create_card(suit: Card.Suit, rank: Card.Rank) -> Card:
	# 这里应该实例化Card场景，暂时创建一个简单的Card对象
	var card = Card.new()
	card.suit = suit
	card.rank = rank
	return card

func shuffle():
	# 洗牌
	for i in range(cards.size()):
		var j = randi() % cards.size()
		var temp = cards[i]
		cards[i] = cards[j]
		cards[j] = temp
	print("洗牌完成")

func deal_cards(num_players: int) -> Array:
	# 发牌，返回每个玩家的手牌
	if cards.size() < 54:
		print("错误：牌数不足")
		return []
	
	var player_hands: Array = []
	for i in range(num_players):
		player_hands.append([])
	
	# 每人发17张牌
	var cards_per_player = 17
	var card_index = 0
	
	for round in range(cards_per_player):
		for player in range(num_players):
			if card_index < cards.size():
				player_hands[player].append(cards[card_index])
				card_index += 1
	
	# 剩余3张作为底牌
	var bottom_cards = []
	while card_index < cards.size():
		bottom_cards.append(cards[card_index])
		card_index += 1
	
	print("发牌完成，每人 ", cards_per_player, " 张，底牌 ", bottom_cards.size(), " 张")
	return [player_hands, bottom_cards]

func get_remaining_cards() -> Array[Card]:
	return cards

func is_empty() -> bool:
	return cards.is_empty()

func draw_card() -> Card:
	if not is_empty():
		return cards.pop_back()
	return null

func add_card(card: Card):
	cards.append(card)

func get_card_count() -> int:
	return cards.size()
