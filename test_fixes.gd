extends Node

# 测试修复后的脚本是否能正常工作

func _ready():
	print("开始测试修复...")
	
	# 测试Card类
	test_card()
	
	# 测试GameManager类
	test_game_manager()
	
	# 测试Player类
	test_player()
	
	# 测试Deck类
	test_deck()
	
	print("所有测试完成！")

func test_card():
	print("测试Card类...")
	var CardScript = load("res://scripts/Card.gd")
	var card = CardScript.new()
	card.suit = "红桃"
	card.rank = "A"
	card.is_face_up = true
	
	print("卡牌创建成功: ", card.get_card_name())
	print("卡牌值: ", card.get_card_value())

func test_game_manager():
	print("测试GameManager类...")
	var GameManagerScript = load("res://scripts/GameManager.gd")
	var game_manager = GameManagerScript.new()
	
	print("GameManager创建成功")
	print("当前状态: ", game_manager.current_state)

func test_player():
	print("测试Player类...")
	var PlayerScript = load("res://scripts/Player.gd")
	var player = PlayerScript.new()
	player.player_name = "测试玩家"
	player.player_index = 0
	
	print("Player创建成功: ", player.player_name)

func test_deck():
	print("测试Deck类...")
	var DeckScript = load("res://scripts/Deck.gd")
	var deck = DeckScript.new()
	deck.initialize_deck()
	
	print("Deck创建成功，卡牌数量: ", deck.get_card_count())
