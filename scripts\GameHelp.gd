extends Control

# 游戏帮助界面
@onready var help_text: RichTextLabel = $VBoxContainer/Panel/ScrollContainer/HelpText
@onready var close_button: Button = $VBoxContainer/CloseButton

func _ready():
	close_button.pressed.connect(_on_close_button_pressed)
	setup_help_text()

func _on_close_button_pressed():
	hide()

func setup_help_text():
	var help_content = """
[center][font_size=24][color=yellow]斗地主游戏规则[/color][/font_size][/center]

[font_size=18][color=cyan]基本规则：[/color][/font_size]
• 三人游戏，一人当地主，两人当农民
• 地主先出牌，农民配合对抗地主
• 最先出完手牌的一方获胜

[font_size=18][color=cyan]牌型说明：[/color][/font_size]

[color=lime]单张：[/color] 任意一张牌
[color=lime]对子：[/color] 两张相同点数的牌
[color=lime]三张：[/color] 三张相同点数的牌
[color=lime]三带一：[/color] 三张相同点数 + 一张单牌
[color=lime]三带二：[/color] 三张相同点数 + 一对
[color=lime]顺子：[/color] 五张或以上连续的单牌（不包括2和王）
[color=lime]连对：[/color] 三对或以上连续的对子
[color=lime]飞机：[/color] 两个或以上连续的三张
[color=lime]炸弹：[/color] 四张相同点数的牌
[color=lime]火箭：[/color] 大王 + 小王

[font_size=18][color=cyan]牌的大小：[/color][/font_size]
3 < 4 < 5 < 6 < 7 < 8 < 9 < 10 < J < Q < K < A < 2 < 小王 < 大王

[font_size=18][color=cyan]出牌规则：[/color][/font_size]
• 必须出相同牌型且更大的牌
• 炸弹可以压任何非炸弹牌型
• 火箭是最大的牌型
• 如果无法出牌可以选择"过"

[font_size=18][color=cyan]操作说明：[/color][/font_size]
• [color=yellow]鼠标左键[/color]：选择/取消选择卡牌
• [color=yellow]出牌按钮[/color]：出选中的牌
• [color=yellow]过牌按钮[/color]：放弃本轮出牌
• [color=yellow]新游戏按钮[/color]：开始新的游戏

[font_size=18][color=cyan]获胜条件：[/color][/font_size]
• 地主先出完牌：地主获胜
• 任一农民先出完牌：农民获胜
"""
	
	help_text.text = help_content
