extends Node

# 玩家类
enum PlayerType { HUMAN, AI }

@export var player_type: PlayerType = PlayerType.HUMAN
@export var player_name: String = "玩家"
@export var is_landlord: bool = false

var hand_cards = []
var selected_cards = []

signal cards_played(cards)
signal turn_passed()

func _ready():
	pass

func add_cards(cards):
	for card in cards:
		hand_cards.append(card)
	sort_hand_cards()

func add_card(card):
	hand_cards.append(card)
	sort_hand_cards()

func remove_cards(cards):
	for card in cards:
		hand_cards.erase(card)

func clear_hand():
	hand_cards.clear()
	selected_cards.clear()

func sort_hand_cards():
	# 按牌的大小排序
	hand_cards.sort_custom(func(a, b): return a.get_card_value() < b.get_card_value())

func get_hand_cards():
	return hand_cards

func get_hand_count() -> int:
	return hand_cards.size()

func select_card(card):
	if card in hand_cards:
		if card in selected_cards:
			selected_cards.erase(card)
			card.set_selected(false)
		else:
			selected_cards.append(card)
			card.set_selected(true)

func clear_selection():
	for card in selected_cards:
		card.set_selected(false)
	selected_cards.clear()

func get_selected_cards():
	return selected_cards

func play_selected_cards():
	if selected_cards.size() > 0:
		var played_cards = selected_cards.duplicate()
		remove_cards(played_cards)
		clear_selection()
		cards_played.emit(played_cards)
		return played_cards
	return []

func pass_turn():
	clear_selection()
	turn_passed.emit()

func has_cards() -> bool:
	return hand_cards.size() > 0

func can_play_cards(cards) -> bool:
	# 检查是否可以出这些牌（基础验证）
	if cards.is_empty():
		return false

	# 检查是否都在手牌中
	for card in cards:
		if card not in hand_cards:
			return false

	return true

# AI相关方法
func ai_make_decision(last_played_cards):
	if player_type != PlayerType.AI:
		return []

	var DoudizhuRulesScript = load("res://scripts/DoudizhuRules.gd")

	# 如果没有上家出牌，出最小的单张
	if last_played_cards.is_empty():
		if hand_cards.size() > 0:
			return [hand_cards[0]]
	else:
		# 尝试找到能压过上家的牌
		var playable_cards = find_playable_cards(last_played_cards, DoudizhuRulesScript)
		if playable_cards.size() > 0:
			return playable_cards

	# 无法出牌，选择过牌
	return []

func find_playable_cards(target_cards, rules_script):
	# 尝试不同的牌型组合

	# 1. 尝试单张
	if target_cards.size() == 1:
		var target_type = rules_script.analyze_cards(target_cards)
		for card in hand_cards:
			var test_cards = [card]
			if rules_script.can_beat(test_cards, target_cards):
				return test_cards

	# 2. 尝试对子
	elif target_cards.size() == 2:
		var pairs = find_pairs()
		for pair in pairs:
			if rules_script.can_beat(pair, target_cards):
				return pair

	# 3. 尝试炸弹（可以打任何牌）
	var bombs = find_bombs()
	if bombs.size() > 0:
		return bombs[0]

	# 4. 尝试火箭（双王）
	var rocket = find_rocket()
	if rocket.size() > 0:
		return rocket

	return []

func find_pairs() -> Array:
	var rank_counts = {}
	for card in hand_cards:
		if rank_counts.has(card.rank):
			rank_counts[card.rank].append(card)
		else:
			rank_counts[card.rank] = [card]

	var pairs = []
	for rank in rank_counts.keys():
		if rank_counts[rank].size() >= 2:
			pairs.append([rank_counts[rank][0], rank_counts[rank][1]])

	return pairs

func find_bombs() -> Array:
	var rank_counts = {}
	for card in hand_cards:
		if rank_counts.has(card.rank):
			rank_counts[card.rank].append(card)
		else:
			rank_counts[card.rank] = [card]

	var bombs = []
	for rank in rank_counts.keys():
		if rank_counts[rank].size() == 4:
			bombs.append(rank_counts[rank])

	return bombs

func find_rocket() -> Array:
	var small_joker = null
	var big_joker = null

	for card in hand_cards:
		if card.rank == 16:  # SMALL_JOKER
			small_joker = card
		elif card.rank == 17:  # BIG_JOKER
			big_joker = card

	if small_joker and big_joker:
		return [small_joker, big_joker]

	return []

func is_ai() -> bool:
	return player_type == PlayerType.AI

func is_human() -> bool:
	return player_type == PlayerType.HUMAN
