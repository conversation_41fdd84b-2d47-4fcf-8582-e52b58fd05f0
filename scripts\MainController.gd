extends Control

# 主控制器，连接UI和游戏逻辑
@onready var game_manager: GameManager = $GameManager
@onready var player_hand_area: CardArea = $GameAreas/PlayerHandArea
@onready var ai1_hand_area: CardArea = $GameAreas/AI1HandArea
@onready var ai2_hand_area: CardArea = $GameAreas/AI2HandArea
@onready var play_area: CardArea = $GameAreas/PlayArea

@onready var game_state_label: Label = $UI/GameInfo/VBoxContainer/GameStateLabel
@onready var current_player_label: Label = $UI/GameInfo/VBoxContainer/CurrentPlayerLabel
@onready var last_played_label: Label = $UI/GameInfo/VBoxContainer/LastPlayedLabel
@onready var player_cards_label: Label = $UI/GameInfo/VBoxContainer/PlayerCardsLabel

@onready var play_button: Button = $UI/Controls/VBoxContainer/PlayButton
@onready var pass_button: Button = $UI/Controls/VBoxContainer/PassButton
@onready var new_game_button: Button = $UI/Controls/VBoxContainer/NewGameButton

var card_areas: Array[CardArea] = []
var audio_manager: AudioManager

func _ready():
	# 初始化音效管理器
	audio_manager = AudioManager.new()
	add_child(audio_manager)

	# 设置卡牌区域数组
	card_areas = [player_hand_area, ai1_hand_area, ai2_hand_area]
	
	# 连接游戏管理器信号
	game_manager.game_state_changed.connect(_on_game_state_changed)
	game_manager.turn_changed.connect(_on_turn_changed)
	game_manager.cards_played.connect(_on_cards_played)
	game_manager.game_over.connect(_on_game_over)
	
	# 连接UI按钮
	play_button.pressed.connect(_on_play_button_pressed)
	pass_button.pressed.connect(_on_pass_button_pressed)
	new_game_button.pressed.connect(_on_new_game_button_pressed)
	
	# 连接玩家手牌区域
	player_hand_area.card_selected.connect(_on_player_card_selected)
	
	# 初始化UI
	update_ui()

	# 延迟设置玩家卡牌
	call_deferred("setup_player_cards")

func _on_game_state_changed(_new_state: GameManager.GameState):
	update_ui()

func _on_turn_changed(player_index: int):
	update_ui()
	
	# 更新按钮状态
	var is_player_turn = (player_index == 0)  # 假设玩家是索引0
	play_button.disabled = not is_player_turn
	pass_button.disabled = not is_player_turn

func _on_cards_played(player_index: int, cards: Array[Card]):
	# 更新出牌区域
	play_area.clear_cards()
	
	# 创建卡牌的视觉副本显示在出牌区域
	for card in cards:
		var card_copy = create_card_visual(card)
		play_area.add_card(card_copy)
	
	# 从对应的手牌区域移除卡牌
	if player_index < card_areas.size():
		card_areas[player_index].remove_cards(cards)
	
	update_ui()

func _on_game_over(winner_index: int):
	var winner_name = game_manager.get_player(winner_index).player_name
	game_state_label.text = "游戏结束！" + winner_name + " 获胜！"
	
	# 禁用游戏按钮
	play_button.disabled = true
	pass_button.disabled = true

func _on_play_button_pressed():
	audio_manager.play_button_click()
	var selected_cards = player_hand_area.get_selected_cards()
	if selected_cards.size() > 0:
		var player = game_manager.get_player(0)  # 玩家是索引0
		if player and player.can_play_cards(selected_cards):
			audio_manager.play_card_play()
			game_manager.play_cards(0, selected_cards)
			player_hand_area.clear_selection()

func _on_pass_button_pressed():
	audio_manager.play_button_click()
	game_manager.pass_turn(0)  # 玩家是索引0
	player_hand_area.clear_selection()

func _on_new_game_button_pressed():
	audio_manager.play_button_click()
	audio_manager.play_game_start()

	# 清空所有卡牌区域
	for area in card_areas:
		area.clear_cards()
	play_area.clear_cards()

	# 重新开始游戏
	game_manager.start_new_game()

	# 延迟设置玩家卡牌，等待游戏管理器初始化完成
	call_deferred("setup_player_cards")

func _on_player_card_selected(card: Card):
	audio_manager.play_card_select()
	var player = game_manager.get_player(0)
	if player:
		player.select_card(card)

func setup_player_cards():
	# 为每个玩家创建卡牌视觉表示
	var players = game_manager.get_players()
	
	for i in range(players.size()):
		if i < card_areas.size():
			var player = players[i]
			var area = card_areas[i]
			
			# 清空区域
			area.clear_cards()
			
			# 添加玩家的手牌
			for card in player.get_hand_cards():
				var card_visual = create_card_visual(card)
				
				# 只有玩家的牌正面朝上
				if i == 0:
					card_visual.is_face_up = true
				else:
					card_visual.is_face_up = false
				
				area.add_card(card_visual)

func create_card_visual(card_data: Card) -> Card:
	# 创建卡牌的视觉表示
	var card_scene = preload("res://scenes/Card.tscn")
	var card_visual = card_scene.instantiate()
	card_visual.suit = card_data.suit
	card_visual.rank = card_data.rank
	card_visual.is_face_up = card_data.is_face_up
	return card_visual

func update_ui():
	# 更新游戏状态显示
	var state_text = ""
	match game_manager.current_state:
		GameManager.GameState.WAITING:
			state_text = "等待开始"
		GameManager.GameState.BIDDING:
			state_text = "叫地主阶段"
		GameManager.GameState.PLAYING:
			state_text = "游戏进行中"
		GameManager.GameState.GAME_OVER:
			state_text = "游戏结束"
	
	game_state_label.text = "游戏状态: " + state_text
	
	# 更新当前玩家
	var current_player = game_manager.get_current_player()
	if current_player:
		current_player_label.text = "当前玩家: " + current_player.player_name
	
	# 更新上次出牌
	if game_manager.last_played_cards.size() > 0:
		last_played_label.text = "上次出牌: " + game_manager.get_cards_string(game_manager.last_played_cards)
	else:
		last_played_label.text = "上次出牌: 无"
	
	# 更新玩家手牌数量
	var player = game_manager.get_player(0)
	if player:
		player_cards_label.text = "手牌数量: " + str(player.get_hand_count())
