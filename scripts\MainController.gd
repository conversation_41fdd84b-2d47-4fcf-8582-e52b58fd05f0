extends Control

# 主控制器，连接UI和游戏逻辑
@onready var game_manager = $GameManager
@onready var player_hand_area = $GameAreas/PlayerHandArea
@onready var ai1_hand_area = $GameAreas/AI1HandArea
@onready var ai2_hand_area = $GameAreas/AI2HandArea
@onready var play_area = $GameAreas/PlayArea

@onready var game_state_label: Label = $UI/GameInfo/VBoxContainer/GameStateLabel
@onready var current_player_label: Label = $UI/GameInfo/VBoxContainer/CurrentPlayerLabel
@onready var last_played_label: Label = $UI/GameInfo/VBoxContainer/LastPlayedLabel
@onready var player_cards_label: Label = $UI/GameInfo/VBoxContainer/PlayerCardsLabel

@onready var play_button: Button = $UI/Controls/CenterButtons/PlayButton
@onready var pass_button: Button = $UI/Controls/CenterButtons/PassButton
@onready var new_game_button: Button = $UI/Controls/CornerButtons/NewGameButton
@onready var help_button: Button = $UI/Controls/CornerButtons/HelpButton

# 玩家信息标签
@onready var ai1_label: Label = $GameAreas/PlayerInfos/AI1Info/AI1Label
@onready var ai2_label: Label = $GameAreas/PlayerInfos/AI2Info/AI2Label
@onready var player_label: Label = $GameAreas/PlayerInfos/PlayerInfo/PlayerLabel

var card_areas = []
var audio_manager
var help_dialog

func _ready():
	# 初始化音效管理器
	var AudioManagerScript = load("res://scripts/AudioManager.gd")
	audio_manager = AudioManagerScript.new()
	add_child(audio_manager)

	# 初始化帮助界面
	create_help_dialog()

	# 设置卡牌区域数组
	card_areas = [player_hand_area, ai1_hand_area, ai2_hand_area]

	# 设置玩家手牌区域为扇形排列
	player_hand_area.is_player_hand = true
	
	# 连接游戏管理器信号
	game_manager.game_state_changed.connect(_on_game_state_changed)
	game_manager.turn_changed.connect(_on_turn_changed)
	game_manager.cards_played.connect(_on_cards_played)
	game_manager.game_over.connect(_on_game_over)
	
	# 连接UI按钮
	play_button.pressed.connect(_on_play_button_pressed)
	pass_button.pressed.connect(_on_pass_button_pressed)
	new_game_button.pressed.connect(_on_new_game_button_pressed)
	help_button.pressed.connect(_on_help_button_pressed)
	
	# 连接玩家手牌区域
	player_hand_area.card_selected.connect(_on_player_card_selected)
	
	# 初始化UI
	update_ui()

	# 延迟设置玩家卡牌
	call_deferred("setup_player_cards")

func _on_game_state_changed(_new_state):
	update_ui()

func _on_turn_changed(player_index: int):
	update_ui()
	
	# 更新按钮状态
	var is_player_turn = (player_index == 0)  # 假设玩家是索引0
	play_button.disabled = not is_player_turn
	pass_button.disabled = not is_player_turn

func _on_cards_played(player_index: int, cards):
	# 更新出牌区域
	play_area.clear_cards()
	
	# 创建卡牌的视觉副本显示在出牌区域
	for card in cards:
		var card_copy = create_card_visual(card)
		play_area.add_card(card_copy)
	
	# 从对应的手牌区域移除卡牌
	if player_index < card_areas.size():
		card_areas[player_index].remove_cards(cards)
	
	update_ui()

func _on_game_over(winner_index: int):
	var winner_name = game_manager.get_player(winner_index).player_name
	game_state_label.text = "游戏结束！" + winner_name + " 获胜！"
	
	# 禁用游戏按钮
	play_button.disabled = true
	pass_button.disabled = true

func _on_play_button_pressed():
	audio_manager.play_button_click()
	var selected_cards = player_hand_area.get_selected_cards()
	print("选中的卡牌数量: ", selected_cards.size())

	if selected_cards.size() > 0:
		# 验证出牌是否有效
		if is_valid_play(selected_cards):
			audio_manager.play_card_play()
			# 从玩家手牌中移除这些卡牌
			var player = game_manager.get_player(0)
			if player:
				# 找到对应的逻辑卡牌并移除
				var cards_to_remove = []
				for visual_card in selected_cards:
					for logic_card in player.get_hand_cards():
						if logic_card.suit == visual_card.suit and logic_card.rank == visual_card.rank:
							cards_to_remove.append(logic_card)
							break

				game_manager.play_cards(0, cards_to_remove)
				player_hand_area.clear_selection()
		else:
			print("无效的出牌组合")
	else:
		print("请先选择要出的牌")

func _on_pass_button_pressed():
	audio_manager.play_button_click()
	game_manager.pass_turn(0)  # 玩家是索引0
	player_hand_area.clear_selection()

func _on_new_game_button_pressed():
	audio_manager.play_button_click()
	audio_manager.play_game_start()

	# 清空所有卡牌区域
	for area in card_areas:
		area.clear_cards()
	play_area.clear_cards()

	# 重新开始游戏
	game_manager.start_new_game()

	# 延迟设置玩家卡牌，等待游戏管理器初始化完成
	call_deferred("setup_player_cards")

func _on_help_button_pressed():
	audio_manager.play_button_click()
	help_dialog.show()

func _on_player_card_selected(card):
	audio_manager.play_card_select()
	# 直接切换卡牌的选择状态
	card.toggle_selection()
	print("卡牌被选择: ", card.get_card_name(), " 选择状态: ", card.is_selected)

func setup_player_cards():
	# 为每个玩家创建卡牌视觉表示
	var players = game_manager.get_players()
	
	for i in range(players.size()):
		if i < card_areas.size():
			var player = players[i]
			var area = card_areas[i]
			
			# 清空区域
			area.clear_cards()
			
			# 添加玩家的手牌
			for card in player.get_hand_cards():
				var card_visual = create_card_visual(card)
				
				# 只有玩家的牌正面朝上
				if i == 0:
					card_visual.is_face_up = true
				else:
					card_visual.is_face_up = false
				
				area.add_card(card_visual)

func create_card_visual(card_data):
	# 创建卡牌的视觉表示
	# 使用Card.tscn场景文件
	var CardScene = load("res://scenes/Card.tscn")
	var card_visual = CardScene.instantiate()
	card_visual.suit = card_data.suit
	card_visual.rank = card_data.rank
	card_visual.is_face_up = card_data.is_face_up
	return card_visual

func create_help_dialog():
	# 创建帮助对话框
	help_dialog = Control.new()
	help_dialog.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	help_dialog.visible = false
	add_child(help_dialog)

	# 背景
	var background = ColorRect.new()
	background.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	background.color = Color(0, 0, 0, 0.7)
	help_dialog.add_child(background)

	# 主容器
	var vbox = VBoxContainer.new()
	vbox.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	vbox.custom_minimum_size = Vector2(800, 600)
	vbox.position = Vector2(-400, -300)
	help_dialog.add_child(vbox)

	# 面板
	var panel = Panel.new()
	var style_box = StyleBoxFlat.new()
	style_box.bg_color = Color(0, 0, 0, 0.8)
	style_box.border_color = Color(1, 1, 1, 1)
	style_box.border_width_left = 2
	style_box.border_width_top = 2
	style_box.border_width_right = 2
	style_box.border_width_bottom = 2
	style_box.corner_radius_top_left = 10
	style_box.corner_radius_top_right = 10
	style_box.corner_radius_bottom_left = 10
	style_box.corner_radius_bottom_right = 10
	panel.add_theme_stylebox_override("panel", style_box)
	panel.size_flags_vertical = Control.SIZE_EXPAND_FILL
	vbox.add_child(panel)

	# 滚动容器
	var scroll = ScrollContainer.new()
	scroll.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	scroll.add_theme_constant_override("margin_left", 10)
	scroll.add_theme_constant_override("margin_top", 10)
	scroll.add_theme_constant_override("margin_right", 10)
	scroll.add_theme_constant_override("margin_bottom", 10)
	panel.add_child(scroll)

	# 帮助文本
	var help_text = RichTextLabel.new()
	help_text.bbcode_enabled = true
	help_text.fit_content = true
	help_text.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	help_text.size_flags_vertical = Control.SIZE_EXPAND_FILL
	help_text.text = get_help_content()
	scroll.add_child(help_text)

	# 关闭按钮
	var close_button = Button.new()
	close_button.text = "关闭"
	close_button.pressed.connect(func(): help_dialog.hide())
	vbox.add_child(close_button)

func get_help_content() -> String:
	return """[center][font_size=24][color=yellow]斗地主游戏规则[/color][/font_size][/center]

[font_size=18][color=cyan]基本规则：[/color][/font_size]
• 三人游戏，一人当地主，两人当农民
• 地主先出牌，农民配合对抗地主
• 最先出完手牌的一方获胜

[font_size=18][color=cyan]牌型说明：[/color][/font_size]

[color=lime]单张：[/color] 任意一张牌
[color=lime]对子：[/color] 两张相同点数的牌
[color=lime]三张：[/color] 三张相同点数的牌
[color=lime]三带一：[/color] 三张相同点数 + 一张单牌
[color=lime]三带二：[/color] 三张相同点数 + 一对
[color=lime]顺子：[/color] 五张或以上连续的单牌（不包括2和王）
[color=lime]连对：[/color] 三对或以上连续的对子
[color=lime]飞机：[/color] 两个或以上连续的三张
[color=lime]炸弹：[/color] 四张相同点数的牌
[color=lime]火箭：[/color] 大王 + 小王

[font_size=18][color=cyan]牌的大小：[/color][/font_size]
3 < 4 < 5 < 6 < 7 < 8 < 9 < 10 < J < Q < K < A < 2 < 小王 < 大王

[font_size=18][color=cyan]出牌规则：[/color][/font_size]
• 必须出相同牌型且更大的牌
• 炸弹可以压任何非炸弹牌型
• 火箭是最大的牌型
• 如果无法出牌可以选择"过"

[font_size=18][color=cyan]操作说明：[/color][/font_size]
• [color=yellow]鼠标左键[/color]：选择/取消选择卡牌
• [color=yellow]出牌按钮[/color]：出选中的牌
• [color=yellow]过牌按钮[/color]：放弃本轮出牌
• [color=yellow]新游戏按钮[/color]：开始新的游戏

[font_size=18][color=cyan]获胜条件：[/color][/font_size]
• 地主先出完牌：地主获胜
• 任一农民先出完牌：农民获胜"""

func update_player_info_labels():
	# 更新所有玩家的信息显示
	var players = game_manager.get_players()

	if players.size() >= 3:
		# 玩家
		var player = players[0]
		var player_text = player.player_name + "\n" + str(player.get_hand_count()) + "张"
		if player.is_landlord:
			player_text += "\n地主"
		player_label.text = player_text

		# AI1
		var ai1 = players[1]
		var ai1_text = ai1.player_name + "\n" + str(ai1.get_hand_count()) + "张"
		if ai1.is_landlord:
			ai1_text += "\n地主"
		ai1_label.text = ai1_text

		# AI2
		var ai2 = players[2]
		var ai2_text = ai2.player_name + "\n" + str(ai2.get_hand_count()) + "张"
		if ai2.is_landlord:
			ai2_text += "\n地主"
		ai2_label.text = ai2_text

func is_valid_play(cards) -> bool:
	# 基础验证：至少要有一张牌
	if cards.size() == 0:
		return false

	# 使用斗地主规则验证
	var DoudizhuRulesScript = load("res://scripts/DoudizhuRules.gd")
	var card_type = DoudizhuRulesScript.analyze_cards(cards)

	# 检查是否是有效牌型
	if card_type.type == DoudizhuRulesScript.CardType.INVALID:
		print("无效牌型")
		return false

	# 检查是否能压过上家的牌
	var last_cards = game_manager.last_played_cards
	if not DoudizhuRulesScript.can_beat(cards, last_cards):
		print("无法压过上家的牌")
		return false

	print("有效出牌: ", DoudizhuRulesScript.CardType.keys()[card_type.type])
	return true

func update_ui():
	# 更新游戏状态显示
	var state_text = ""
	match game_manager.current_state:
		game_manager.GameState.WAITING:
			state_text = "等待开始"
		game_manager.GameState.BIDDING:
			state_text = "叫地主阶段"
		game_manager.GameState.PLAYING:
			state_text = "游戏进行中"
		game_manager.GameState.GAME_OVER:
			state_text = "游戏结束"

	game_state_label.text = "游戏状态: " + state_text
	
	# 更新当前玩家
	var current_player = game_manager.get_current_player()
	if current_player:
		current_player_label.text = "当前玩家: " + current_player.player_name
	
	# 更新上次出牌
	if game_manager.last_played_cards.size() > 0:
		last_played_label.text = "上次出牌: " + game_manager.get_cards_string(game_manager.last_played_cards)
	else:
		last_played_label.text = "上次出牌: 无"
	
	# 更新玩家手牌数量
	update_player_info_labels()
