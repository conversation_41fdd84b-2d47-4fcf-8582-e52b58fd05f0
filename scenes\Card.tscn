[gd_scene load_steps=4 format=3 uid="uid://bqxvhqxqxqxqx"]

[ext_resource type="Script" path="res://scripts/Card.gd" id="1_1"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(1, 1, 0, 0.3)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(1, 1, 0, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_2"]
bg_color = Color(1, 1, 1, 1)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0, 0, 0, 1)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="Card" type="Control"]
custom_minimum_size = Vector2(80, 120)
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_1")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(1, 1, 1, 1)
theme_override_styles/normal = SubResource("StyleBoxFlat_2")

[node name="CardSprite" type="Sprite2D" parent="."]
position = Vector2(40, 60)
scale = Vector2(0.8, 0.8)

[node name="CardBack" type="Sprite2D" parent="."]
position = Vector2(40, 60)
scale = Vector2(0.8, 0.8)

[node name="SelectionHighlight" type="ColorRect" parent="."]
visible = false
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
color = Color(1, 1, 0, 0.3)
theme_override_styles/normal = SubResource("StyleBoxFlat_1")

[node name="CardText" type="Label" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -10.0
offset_right = 20.0
offset_bottom = 10.0
text = "A♠"
horizontal_alignment = 1
vertical_alignment = 1
