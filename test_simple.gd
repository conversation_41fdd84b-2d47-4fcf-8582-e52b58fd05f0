extends Node

# 简单测试脚本，验证基本类是否工作

func _ready():
	print("开始简单测试...")
	
	# 测试Card类
	test_card_basic()
	
	# 测试Deck类
	test_deck_basic()
	
	print("简单测试完成！")

func test_card_basic():
	print("测试Card类...")
	var card = Card.new()
	print("Card创建成功")
	print("默认花色: ", card.suit)
	print("默认点数: ", card.rank)
	print("卡牌名称: ", card.get_card_name())
	print("卡牌值: ", card.get_card_value())

func test_deck_basic():
	print("测试Deck类...")
	var deck = Deck.new()
	add_child(deck)
	print("Deck创建成功")
	print("牌堆大小: ", deck.get_card_count())
	
	# 清理
	deck.queue_free()
